import { NextRequest, NextResponse } from "next/server";

interface UserStarGift {
  name_hidden?: boolean;
  unsaved?: boolean;
  from_id?: number;
  date: number;
  gift: {
    limited?: boolean;
    sold_out?: boolean;
    birthday?: boolean;
    id: number;
    sticker: {
      file_id: string;
      file_unique_id: string;
      width: number;
      height: number;
      is_animated: boolean;
      is_video: boolean;
      thumb?: {
        file_id: string;
        file_unique_id: string;
        width: number;
        height: number;
        file_size?: number;
      };
      emoji?: string;
      set_name?: string;
      file_size?: number;
    };
    stars: number;
    availability_remains?: number;
    availability_total?: number;
    convert_stars: number;
    first_sale_date?: number;
    last_sale_date?: number;
  };
  message?: {
    text: string;
    entities?: any[];
  };
  msg_id?: number;
  convert_stars?: number;
}

interface UserStarGiftsResponse {
  count: number;
  gifts: UserStarGift[];
  next_offset?: string;
  users: any[];
}

// Validate Telegram initData (basic validation)
function validateInitData(initData: string): boolean {
  try {
    // Basic validation - check if it contains required fields
    return initData.includes("user=") && initData.includes("auth_date=");
  } catch {
    return false;
  }
}

// Transform Telegram API response to our interface
function transformTelegramGift(telegramGift: any): UserStarGift {
  return {
    name_hidden: telegramGift.nameHidden || false,
    unsaved: telegramGift.unsaved || false,
    from_id: telegramGift.fromId?.value || undefined,
    date: telegramGift.date || Math.floor(Date.now() / 1000),
    gift: {
      limited: telegramGift.gift?.limited || false,
      sold_out: telegramGift.gift?.soldOut || false,
      birthday: telegramGift.gift?.birthday || false,
      id: telegramGift.gift?.id?.value || 0,
      sticker: {
        file_id: telegramGift.gift?.sticker?.id?.toString() || "",
        file_unique_id:
          telegramGift.gift?.sticker?.fileReference?.toString() || "",
        width: telegramGift.gift?.sticker?.w || 512,
        height: telegramGift.gift?.sticker?.h || 512,
        is_animated: telegramGift.gift?.sticker?.animated || false,
        is_video: telegramGift.gift?.sticker?.videoSticker || false,
        emoji: telegramGift.gift?.sticker?.emoji || "⭐",
        set_name: telegramGift.gift?.sticker?.setName || "star_gifts",
      },
      stars: telegramGift.gift?.stars?.value || 0,
      availability_remains: telegramGift.gift?.availabilityRemains || undefined,
      availability_total: telegramGift.gift?.availabilityTotal || undefined,
      convert_stars: telegramGift.gift?.convertStars?.value || 0,
      first_sale_date: telegramGift.gift?.firstSaleDate || undefined,
      last_sale_date: telegramGift.gift?.lastSaleDate || undefined,
    },
    message: telegramGift.message
      ? {
          text: telegramGift.message.message || "",
          entities: telegramGift.message.entities || [],
        }
      : undefined,
    msg_id: telegramGift.msgId || undefined,
    convert_stars: telegramGift.convertStars?.value || undefined,
  };
}

// Get user star gifts - Bot API Limitation
async function getUserStarGifts(
  userId: number,
  offset: string = "",
  limit: number = 20
): Promise<UserStarGiftsResponse> {
  // IMPORTANT: Bot API Limitation
  // According to the Telegram Bot API documentation (https://core.telegram.org/bots/api),
  // bots do not have access to fetch regular users' Star Gifts collections.
  //
  // Available gift-related methods for bots:
  // 1. sendGift - Send gifts to users/chats
  // 2. getBusinessAccountGifts - Get gifts for managed business accounts only
  // 3. Gift-related webhook updates when gifts are sent/received
  //
  // The GetSavedStarGifts method is only available in the MTProto API for user clients,
  // not for bots. This is a privacy protection - bots shouldn't access users' personal
  // gift collections.
  //
  // Possible solutions:
  // 1. Use user authentication instead of bot authentication (requires user login)
  // 2. Store gift data when received through webhook updates
  // 3. Use a different approach that doesn't require accessing user's gift collection

  console.log(`Bot API limitation: Cannot fetch Star Gifts for user ${userId}`);
  console.log(
    "Bots do not have access to users' personal Star Gift collections"
  );

  // Return empty response with explanation
  return {
    count: 0,
    gifts: [],
    next_offset: undefined,
    users: [],
  };
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { user_id, offset = "", limit = 20, initData } = body;

    // Validate required parameters
    if (!user_id) {
      return NextResponse.json(
        { error: "user_id is required" },
        { status: 400 }
      );
    }

    if (!initData) {
      return NextResponse.json(
        { error: "initData is required for authentication" },
        { status: 400 }
      );
    }

    // Validate initData format
    if (!validateInitData(initData)) {
      return NextResponse.json(
        { error: "Invalid initData format" },
        { status: 400 }
      );
    }

    // Get user star gifts using real Telegram API
    const response = await getUserStarGifts(user_id, offset, limit);

    return NextResponse.json({
      success: true,
      data: response,
    });
  } catch (error) {
    console.error("Error fetching user gifts:", error);

    return NextResponse.json(
      {
        error: "Failed to fetch user gifts",
        details: error instanceof Error ? error.message : "Unknown error",
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  return NextResponse.json(
    { error: "Method not allowed. Use POST instead." },
    { status: 405 }
  );
}
